import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import "../../styles/Signup.css";
import { FaRegUser } from "react-icons/fa";
import { MdMailOutline } from "react-icons/md";

import {
  FaBook,
  FaChalkboardTeacher,
  FaUser,
  FaEnvelope,
  FaCheck,
} from "react-icons/fa";
import { FaMobile } from "react-icons/fa6";
import {
  register,
  reset,
  googleSignIn,
  googleSignUp,
} from "../../redux/slices/authSlice";
import toast from "../../utils/toast";
import GoogleSignInButton from "../../components/common/GoogleSignInButton";
import PhoneInput from "../../components/common/PhoneInput";
import { validatePhoneNumber } from "../../utils/phoneValidation";
import firebaseService from "../../services/firebaseService";
import { getSellerRedirectPath } from "../../utils/sellerUtils";

const Signup = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLoading, isError, isSuccess, error } = useSelector(
    (state) => state.auth
  );

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    countryCode: "+1",
    accountType: "learn", // Default to buyer role
    agreeToTerms: false,
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const handleAccountTypeChange = (type) => {
    setFormData({
      ...formData,
      accountType: type,
    });
  };

  const handleCountryCodeChange = (e) => {
    setFormData({
      ...formData,
      countryCode: e.target.value,
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    // Validate phone number only if provided (now optional)
    if (formData.phone) {
      const phoneValidation = validatePhoneNumber(formData.countryCode, formData.phone);
      if (!phoneValidation.isValid) {
        newErrors.phone = phoneValidation.error;
      }
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "You must agree to the terms and conditions";
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    dispatch(reset());

    try {
      // Prepare registration data
      const registrationData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        role: formData.accountType === "learn" ? "buyer" : "seller",
      };

      // Add mobile if provided
      if (formData.phone && formData.countryCode) {
        registrationData.mobile = `${formData.countryCode}${formData.phone}`;
      }

      // Dispatch register action and wait for response
      const result = await dispatch(register(registrationData)).unwrap();

      // Show success message
      toast.auth.registrationSuccess();

      // Navigate to OTP verification page with user ID and email
      navigate("/otp-verification", {
        state: {
          userId: result.userId,
          email: formData.email,
          phoneNumber: formData.phone && formData.countryCode ? `${formData.countryCode} ${formData.phone}` : null,
          cooldownSeconds: result.cooldownSeconds || 60,
          isLogin: false,
          developmentOtp: result.developmentOtp, // Pass development OTP if available
        },
      });
    } catch (error) {
      // Handle different types of errors
      console.error("Registration error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Registration failed. Please try again.";

      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Show specific error messages for common cases
      if (errorMessage.includes("Email already registered")) {
        toast.error("This email is already registered. Please try logging in instead.");
      } else if (errorMessage.includes("Mobile number already registered")) {
        toast.error("This mobile number is already registered. Please try logging in instead.");
      } else if (errorMessage.includes("already registered")) {
        toast.error("This email or mobile number is already registered. Please try logging in instead.");
      } else {
        // Show the actual backend error message
        toast.error(errorMessage);
      }
    }
  };

  // Handle Google Sign-Up
  const handleGoogleSignUp = async () => {
    // Check if user has agreed to terms before proceeding
    if (!formData.agreeToTerms) {
      setErrors({
        ...errors,
        agreeToTerms: "You must agree to the terms and conditions to sign up"
      });
      toast.error("Please agree to the terms and conditions to continue");
      return;
    }

    // Clear any existing terms error
    if (errors.agreeToTerms) {
      setErrors({
        ...errors,
        agreeToTerms: null
      });
    }

    try {
      dispatch(reset());

      // Check if Firebase is initialized
      if (!firebaseService.isInitialized()) {
        toast.error(
          "Firebase is not initialized. Please check your configuration."
        );
        return;
      }

      // Sign in with Google using Firebase
      const result = await firebaseService.signInWithGoogle();

      // Check if user already exists
      try {
        const response = await dispatch(googleSignIn(result.idToken)).unwrap();

        // User already exists - redirect to login
        toast.info("Account already exists. Redirecting to dashboard...");

        // Navigate based on user role and onboarding status
        if (response.user.role === "buyer") {
          navigate("/content");
        } else if (response.user.role === "seller") {
          // For sellers, check onboarding status and redirect accordingly
          const redirectPath = getSellerRedirectPath(response.user);
          navigate(redirectPath);
        } else if (response.user.role === "admin") {
          navigate("/admin/dashboard");
        } else {
          navigate("/");
        }
      } catch (signInError) {
        // User doesn't exist - create new account with selected role
        const errorMessage =
          typeof signInError === "string"
            ? signInError
            : signInError?.message || "";
        if (
          errorMessage.includes("not found") ||
          errorMessage.includes("does not exist")
        ) {
          // Default to buyer role for Google sign-ups
          const role = "buyer";

          try {
            await dispatch(
              googleSignUp({ idToken: result.idToken, role })
            ).unwrap();

            // Success - user created and logged in
            toast.auth.registrationSuccess();

            // Navigate based on selected role and onboarding status
            if (role === "buyer") {
              navigate("/content");
            } else if (role === "seller") {
              // For new sellers, redirect to onboarding (they won't have completed it yet)
              navigate("/seller-onboarding");
            } else {
              navigate("/");
            }
          } catch (signUpError) {
            throw signUpError;
          }
        } else {
          throw signInError;
        }
      }
    } catch (error) {
      console.error("Google sign-up error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Failed to sign up with Google. Please try again.";

      if (typeof error === "string") {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Show the actual backend error message
      toast.error(errorMessage);
    }
  };

  return (
    <div className="signup__page">
      <div className="signup__container">
        <h1 className="signup__title">Sign up to your account</h1>

        <form onSubmit={handleSubmit} className="signup__form">
          <div className="signup__form-row">
            <div className="signup__input-container">
              <div className="signup__input-icon">
                <FaRegUser />
              </div>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                placeholder="First Name"
                className={`signup__input ${errors.firstName ? "signup__input--error" : ""
                  }`}
                required
              />
              {errors.firstName && (
                <p className="signup__error">{errors.firstName}</p>
              )}
            </div>

            <div className="signup__input-container">
              <div className="signup__input-icon">
                <FaRegUser />
              </div>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                placeholder="Last Name"
                className={`signup__input ${errors.lastName ? "signup__input--error" : ""
                  }`}
                required
              />
              {errors.lastName && (
                <p className="signup__error">{errors.lastName}</p>
              )}
            </div>
          </div>

          <div className="signup__input-container">
            <div className="signup__input-icon">
              <MdMailOutline />
            </div>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter Email Address"
              className={`signup__input ${errors.email ? "signup__input--error" : ""
                }`}
              required
            />
            {errors.email && <p className="signup__error">{errors.email}</p>}
          </div>

          <PhoneInput
            countryCode={formData.countryCode}
            phone={formData.phone}
            onCountryCodeChange={handleCountryCodeChange}
            onPhoneChange={handleChange}
            error={errors.phone}
            placeholder="00000 00000 (Optional)"
            className="signup__phone-container"
            required={false}
            name="phone"
          />

          <div className="signup__terms">
            <input
              type="checkbox"
              id="agreeToTerms"
              name="agreeToTerms"
              checked={formData.agreeToTerms}
              onChange={handleChange}
              className={`signup__checkbox ${errors.agreeToTerms ? "signup__checkbox--error" : ""}`}
              required
            />
            <label htmlFor="agreeToTerms" className="signup__terms-label">
              <span className="signup__required-asterisk">*</span>
              By signing up, you agree to receive SMS messages from XOsportshub operated by EMA Ventures LLC at (*************. Message and data rates may apply. Reply STOP to unsubscribe, HELP for help. See our
              &nbsp;<Link to="/cms/privacy-policy" className="signup__terms-link">
                Privacy Policy
              </Link>
              &nbsp; and &nbsp;
              <Link to="/cms/terms-and-conditions" className="signup__terms-link">
                Terms of Service
              </Link>.
            </label>

          </div>

          <button type="submit" className="signup__button" disabled={isLoading}>
            {isLoading ? "Creating Account..." : "Create Your Account"}
          </button>

          <div className="signup__divider">
            <span>or</span>
          </div>

          <GoogleSignInButton
            onClick={handleGoogleSignUp}
            isLoading={isLoading}
            text="Sign up with Google"
            variant="secondary"
          />

          <p className="signup__login-link mt-10">
            Do you have an account?{" "}
            <Link to="/auth" className="signup__link">
              Sign In
            </Link>
          </p>
        </form>
      </div>
    </div>
  );
};

export default Signup;
